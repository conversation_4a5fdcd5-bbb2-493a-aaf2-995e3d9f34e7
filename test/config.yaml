# PixivTagDownloader Configuration File
# Copy this file to config.yaml and modify as needed

# Authentication settings
# REQUIRED: Your Pixiv session cookie
# To get your cookie:
# 1. Login to Pixiv in your browser
# 2. Open browser developer tools (F12)
# 3. Go to Application/Storage -> Cookies -> https://www.pixiv.net
# 4. Copy the entire cookie string
cookie: "__cf_bm=N_o7ktB8SgUsUhRwvLRoUoKW1YQvqbd0pUCnZaEwnF4-1746259001-1.0.1.1-3SgFlatQ9psAvhAB7bQHGCZEVDljo6pgKnp92gOxfpqOSmGKT3qHRpsi8uQKCAqcJoV3qnLQKQbNs9LVWbaSvF_ojyRvoyS8pESEQUKaxoZUDwjpJrgvJKJdS867Y5Jx;__utma=235335808.721382002.1746257217.1746257217.1746257217.1;__utmc=235335808;__utmv=235335808.|2=login%20ever=no=1^3=plan=normal=1^9=p_ab_id=5=1^10=p_ab_id_2=9=1;__utmz=235335808.1746257217.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none);_ga=GA1.1.331186726.1746257217;_ga_75BBYNYN9J=GS1.1.1746257217.1.1.1746257923.0.0.0;_ga_MZ1NL4PHH0=GS1.1.1746257219.1.0.1746257229.0.0.0;a_type=0;b_type=1;c_type=49;cc1=2025-05-03%2016%3A26%3A20;cto_bundle=o55pr19NRUF3cDYlMkY0OHh2ciUyQmdLZ3h1YllSUmlJYjNzTVJsYnFrMjB5ZEFwZ3VUdlN1REs1MDVwNlFvek9kRGF3czJVUnAxUjJDVUFGdGxOMG11TVJsSGJwanMlMkJEaG96YkdKUEZxSnJCWE9xWDBzc0xQMTgzbnl4MEIlMkJaUXpIbFFTRlU0VFdOMmtrbFg3TlVRMHE2VEdkdW5TUSUzRCUzRA;device_token=84419d1ecc6a53e35b085ca2d4ac4611;FCNEC=%5B%5B%22AKsRol97srfYkPQJK4NiXdqHv0O8V84zlTQsP87rQwq-F5hUakZF-1BqMEyXdNRIK27Uhb-91kAJZNmspN2yqH0J637nFEz6vBUeQDseLkf6WgjP0ucZTIKf8IJSpgRM3P5mDg5lJrpzSw49Zt0vFmDJf4uDig6q-Q%3D%3D%22%5D%5D;p_ab_d_id=1859539966;p_ab_id=5;p_ab_id_2=9;PHPSESSID=18104148_13ZzxqxPUQBmRaYtuNgCC98qaehNHAD4;privacy_policy_agreement=7;privacy_policy_notification=0;first_visit_datetime_pc=2025-05-03%2016%3A26%3A20;yuid_b=FkmAZDY;"

# Output directory settings
output_dir: "Output"

# Path template settings
# Available variables:
# {uid}, {username}, {pid}, {title}, {type}, {page_index}, {page_count}
# {series_title}, {series_id}, {upload_date}, {tags}, {r18}
# {like_count}, {bookmark_count}, {ext}
path_template:
  directory: "{uid}_{username}/{type}/{series_title}"
  filename: "{upload_date}_{pid}_{title}{page_index}"
  subdirectory: "{upload_date}_{pid}_{title}"  # Template for multi-page artwork subdirectories
  page_index_format: "p{:02d}"  # p00, p01, p02, etc.
  novel_content_separator: "*** Content ***"

# Download settings
download:
  method: "direct"  # direct, aria2c, aria2rpc
  concurrency: 4    # Number of concurrent downloads
  delay_range: "1-3"  # Random delay between requests (seconds)
  conflict_strategy: "skip"  # skip, overwrite, rename
  timeout_minutes: 30  # Overall download timeout in minutes
  
  # Aria2 RPC settings (only used if method is aria2rpc)
  aria2:
    rpc_url: "ws://localhost:6800/jsonrpc"
    secret: ""  # Optional RPC secret
    verify_ssl: true
    ca_cert_path: ""  # Optional custom CA certificate

# HTTP settings
http:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  referer: "https://www.pixiv.net/"
  timeout_seconds: 30
  max_retries: 3
  retry_delay_seconds: 1
  verify_ssl: true
  
  # Custom headers (optional)
  headers:
    Accept-Language: "en-US,en;q=0.9"

# Logging settings
logging:
  level: "info"  # trace, debug, info, warn, error
  file: "log"  # Optional log directory (empty = no file logging, filename auto-generated as YY-MM-DD_HH_MM_SS.log)
  console: true  # Enable console logging
